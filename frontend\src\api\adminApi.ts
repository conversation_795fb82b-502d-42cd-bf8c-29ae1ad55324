import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

// 管理员
export class AdminApi {
  // 获取列表
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: `/system/admin/index`,
      params
    })
  }

  // 新增
  static add(params: any) {
    return request.post<BaseResult>({
      url: `/system/admin/add`,
      data: params
    })
  }

  static edit(id: number, params: any) {
    return request.post<BaseResult>({
      url: `/system/admin/edit/${id}`,
      data: params
    })
  }

  static detail(id: number) {
    return request.get<BaseResult>({
      url: `/system/admin/detail/${id}`
    })
  }

  static delete(id: number) {
    return request.post<BaseResult>({
      url: `/system/admin/delete/${id}`
    })
  }

  /**
   * 获取后台用户下拉选项
   * @param params 查询参数
   */
  static options(params?: any) {
    return request.get<BaseResult>({
      url: '/system/admin/options',
      params
    })
  }

  /*
   * 获取当前管理员信息
   * */
  static info() {
    return request.get<BaseResult>({
      url: `/system/admin/info`
    })
  }

  static permissions() {
    return request.get<BaseResult>({
      url: `/system/admin/permissions`
    })
  }

  static resetPassword(id: number, params: any) {
    return request.post<BaseResult>({
      url: `/system/admin/reset_password/${id}`,
      data: params
    })
  }

  static changePassword(params: any) {
    return request.post<BaseResult>({
      url: `/system/admin/change_password`,
      data: params
    })
  }

  static avatar(params: any) {
    return request.post<BaseResult>({
      url: `/system/admin/avatar`,
      data: params
    })
  }
}
