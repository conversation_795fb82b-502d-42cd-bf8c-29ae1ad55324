import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

// 角色管理
export class RoleApi {
  // 获取角色列表
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: `/system/role/index`,
      params
    })
  }

  // 新增角色
  static add(params: any) {
    return request.post<BaseResult>({
      url: `/system/role/add`,
      data: params
    })
  }

  // 编辑角色
  static edit(id: number, params: any) {
    return request.post<BaseResult>({
      url: `/system/role/edit/${id}`,
      data: params
    })
  }

  // 获取角色详情
  static detail(id: number) {
    return request.get<BaseResult>({
      url: `/system/role/detail/${id}`
    })
  }

  // 删除角色
  static delete(id: number) {
    return request.post<BaseResult>({
      url: `/system/role/delete/${id}`
    })
  }

  static options() {
    return request.get<BaseResult>({
      url: `/system/role/options`
    })
  }

  // 获取角色已分配的菜单权限
  static getRoleMenus(id: number) {
    return request.get<BaseResult>({
      url: `/system/role/menus/${id}`
    })
  }

  // 分配菜单权限
  static assignMenus(id: number, menuIds: number[]) {
    return request.post<BaseResult>({
      url: `/system/role/assign_menus/${id}`,
      data: { menu_ids: menuIds }
    })
  }
}
