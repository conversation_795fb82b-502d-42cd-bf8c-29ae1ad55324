<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;
use think\model\relation\BelongsTo;

/**
 * 文章表模型
 */
class SystemArticle extends BaseModel
{
	// 设置表名
	protected $name = 'system_article';
	
	protected $append = [
		'cover_image_arr'
	];
	
	public function getCoverImageArrAttr($value, $data): array
	{
		return !empty($data['cover_image'])
			? explode(',', $data['cover_image'])
			: [];
	}
	
	public function getContentAttr($value): string
	{
		return htmlspecialchars_decode($value);
	}
	
	/**
	 * 获取默认搜索字段
	 *
	 * @return array
	 */
	public function getDefaultSearchFields(): array
	{
		return [
			'title'        => ['type' => 'like'],
			'category_id'  => ['type' => 'eq'],
			'status'       => ['type' => 'eq'],
			'is_top'       => ['type' => 'eq'],
			'is_hot'       => ['type' => 'eq'],
			'created_at'   => ['type' => 'date'],
			'publish_time' => ['type' => 'date'],
		];
	}
	
	public function category(): BelongsTo
	{
		return $this->belongsTo(SystemArticleCategory::class, 'category_id', 'id')
		            ->bind([
			            'category_name' => 'name'
		            ]);
	}
} 