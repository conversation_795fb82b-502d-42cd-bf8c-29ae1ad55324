<?php
declare(strict_types=1);

namespace app\system\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use app\common\exception\ValidateFailedException;
use app\common\utils\DataPermissionCacheUtil;
use app\system\model\DeptModel;
use app\system\validate\DeptValidate;
use think\exception\ValidateException;

/**
 * 部门服务类
 */
class DeptService extends BaseService
{
	
	public function __construct()
	{
		$this->model = new DeptModel();
		parent::__construct();
	}
	
	/**
	 * 获取部门列表
	 *
	 * @param array $params 查询参数
	 * @return array
	 */
	public function getList(array $params): array
	{
		$where = [];
		
		// 名称搜索
		if (!empty($params['name'])) {
			$where[] = [
				'name',
				'like',
				'%' . $params['name'] . '%'
			];
		}
		
		// 编码搜索
		if (!empty($params['code'])) {
			$where[] = [
				'code',
				'like',
				'%' . $params['code'] . '%'
			];
		}
		
		// 状态搜索
		if (isset($params['status']) && $params['status'] !== '') {
			$where[] = [
				'status',
				'=',
				(int)$params['status']
			];
		}
		
		// 排序规则
		$order = 'sort asc, id asc';
		
		// 不分页，返回所有部门
		$list = $this->getCrudService()
		             ->getList($where, $order);
		
		return $this->buildDeptTree($list->toArray());
	}
	
	/**
	 * 获取部门详情
	 *
	 * @param int $id 部门ID
	 */
	public function getDetail(int $id)
	{
		$info = $this->getCrudService()
		             ->getOne([
			             [
				             'id',
				             '=',
				             $id
			             ],
		             ]);
		
		if ($info->isEmpty()) {
			throw new BusinessException('数据不存在');
		}
		
		return $info;
	}
	
	/**
	 * 创建部门
	 *
	 * @param array $data 部门数据
	 * @return int
	 */
	public function create(array $data): int
	{
		try {
			validate(DeptValidate::class)->check($data);
		}
		catch (ValidateFailedException $e) {
			throw new ValidateException($e->getMessage());
		}
		
		$data['parent_id']  = $data['parent_id'] ?? 0;
		
		$res = $this->model->saveByCreate($data);
		
		$res && DataPermissionCacheUtil::clearAllDataPermissions();
		
		return $res;
	}
	
	/**
	 * 更新部门
	 *
	 * @param int   $id   部门ID
	 * @param array $data 部门数据
	 * @return bool
	 */
	public function update(int $id, array $data): bool
	{
		try {
			validate(DeptValidate::class)->check($data);
		}
		catch (ValidateFailedException $e) {
			throw new ValidateException($e->getMessage());
		}
		
		$info = $this->getCrudService()
		             ->getOne([
			             [
				             'id',
				             '=',
				             $id
			             ],
		             ]);
		
		if ($info->isEmpty()) {
			throw new BusinessException('数据不存在');
		}
		
		$res = $info->saveByUpdate($data);
		
		$res && DataPermissionCacheUtil::clearAllDataPermissions();
		
		return $res;
	}
	
	/**
	 * 删除部门
	 *
	 * @return bool
	 */
	public function delete(int $id): bool
	{
		
		$info = $this->getDetail($id);
		
		// 检查是否有子部门
		$childCount = $this->getCrudService()
		                   ->getCount([
			                   [
				                   'parent_id',
				                   '=',
				                   $id
			                   ],
		                   ]);
		if ($childCount > 0) {
			throw new BusinessException('请先删除子部门');
		}
		
		// 检查是否有用户关联
		$userCount = AdminService::getInstance()
		                         ->getCrudService()
		                         ->getCount([
			                         [
				                         'dept_id',
				                         '=',
				                         $id
			                         ],
		                         ]);
		
		if ($userCount > 0) {
			throw new BusinessException('当前部门存在管理员');
		}
		
		// 执行删除
		$res = $info->delete();
		
		$res && DataPermissionCacheUtil::clearAllDataPermissions();
		
		return $res;
	}
	
	/**
	 * 根据父部门ID获取所有子部门ID
	 *
	 * @param int $pid
	 * @param int $tenantId
	 * @return array
	 */
	public function getDeptIdsByPid(int $pid, int $tenantId): array
	{
		return $this->model->where('parent_id', $pid)
		                   ->where('tenant_id', $tenantId)
		                   ->select()
		                   ->column('id');
	}
	
	/**
	 * 获取部门树形结构（用于下拉选择）
	 *
	 * @param int $adminId
	 * @param int $tenantId
	 * @return array
	 */
	public function getOptions(int $adminId, int $tenantId): array
	{
		$list = $this->model->field('id,parent_id,name')
		                    ->where('tenant_id', $tenantId)
		                    ->where('status', 1)
		                    ->order('sort asc')
		                    ->select();
		return $this->buildDeptTree($list->toArray());
	}
	
	/**
	 * 获取子部门ID列表
	 *
	 * @param int $deptId   部门ID
	 * @param int $tenantId 租户ID
	 * @return array 子部门ID数组
	 */
	public function getChildDeptIds(int $deptId, int $tenantId): array
	{
		return $this->model->where('parent_id', '=', $deptId)
		                   ->where('tenant_id', $tenantId)
		                   ->column('id');
	}
	
	/**
	 * 构建部门树结构
	 *
	 * @param array $list     部门列表
	 * @param int   $parentId 父部门ID
	 * @return array 树结构
	 */
	private function buildDeptTree(array $list, int $parentId = 0): array
	{
		$tree = [];
		
		foreach ($list as $dept) {
			if ($dept['parent_id'] == $parentId) {
				$children = $this->buildDeptTree($list, $dept['id']);
				
				if (!empty($children)) {
					$dept['children'] = $children;
				}
				
				$tree[] = $dept;
			}
		}
		
		return $tree;
	}
	
} 