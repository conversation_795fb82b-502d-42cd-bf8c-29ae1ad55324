<?php
declare(strict_types=1);

namespace app\daily\service;

use app\common\core\base\BaseService;
use app\common\core\crud\traits\CrudServiceTrait;
use app\common\exception\BusinessException;
use app\daily\model\DailyPriceOrder;
use app\daily\model\DailyPriceItem;
use app\ims\model\ImsSupplier;
use app\crm\model\CrmProduct;
use app\workflow\constants\WorkflowStatusConstant;
use app\workflow\interfaces\FormServiceInterface;
use app\workflow\traits\DefaultWorkflowCallbackTrait;
use app\workflow\service\UnifiedWorkflowService;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use think\facade\Log;
use app\common\core\crud\traits\ExportableTrait;
use app\common\core\crud\traits\ImportableTrait;

/**
 * 每日报价单表服务类
 * 实现FormServiceInterface接口，支持工作流表单操作
 */
class DailyPriceOrderService extends BaseService implements FormServiceInterface
{
	use ExportableTrait, ImportableTrait, CrudServiceTrait, DefaultWorkflowCallbackTrait;
	
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		$this->model = new DailyPriceOrder();
		parent::__construct();
	}
	
	
	public function getOrderList(array $params): array
	{
		$where = [];
		
		// 根据approval_status参数构建查询条件
		if (isset($params['approval_status'])) {
			// 指定具体状态
			$where[] = [
				'approval_status',
				'=',
				intval($params['approval_status'])
			];
		}
		else {
			// 默认查询非已通过的数据（兼容原有逻辑）
			$where[] = [
				'approval_status',
				'>',
				2
			];
		}
		
		$count = $this->model->where($where)
		                     ->count();
		$page  = intval($params['page'] ?? 1);
		$limit = intval($params['limit'] ?? 10);
		$list  = $this->model->where($where)
		                     ->order('id', 'desc')
		                     ->page($page, $limit)
		                     ->select();
		return [
			'total' => $count,
			'list'  => $list,
			'page'  => $page,
			'limit' => $limit,
		];
	}
	
	/**
	 * 获取验证规则
	 *
	 * @param string $scene 场景
	 * @return array
	 */
	protected function getValidationRules(string $scene): array
	{
		// 基础规则
		$rules = [
			'price_date' => 'require|date',
			'items'      => 'require|array|min:1',
			// 明细不能为空
		];
		
		// 根据场景返回规则
		return match ($scene) {
			'add' => $rules,
			'edit' => $rules,
			default => [],
		};
	}
	
	/**
	 * 重写添加方法，处理报价明细
	 *
	 * @param array $data 数据
	 * @return int 返回新增记录的ID
	 * @throws DataNotFoundException
	 * @throws DbException
	 * @throws ModelNotFoundException
	 */
	public function add(array $data): int
	{
		// 验证明细数据
		if (empty($data['items']) || !is_array($data['items'])) {
			throw new BusinessException('报价明细不能为空');
		}
		
		$existingOrder = DailyPriceOrder::where('price_date', $data['price_date'])
		                                ->whereIn('approval_status', [
			                                DailyPriceOrder::STATUS_DRAFT,
			                                DailyPriceOrder::STATUS_PENDING,
			                                DailyPriceOrder::STATUS_APPROVED
		                                ])
		                                ->find();
		
		if ($existingOrder) {
			throw new BusinessException('当天已存在报价单');
		}
		
		// 验证明细数据中是否有重复的供应商-产品组合
		$combinations = [];
		foreach ($data['items'] as $item) {
			if (empty($item['supplier_id']) || empty($item['product_id']) || empty($item['unit_price'])) {
				throw new BusinessException('供应商,产品和单价不能为空');
			}
			$key = $item['supplier_id'] . '-' . $item['product_id'];
			if (in_array($key, $combinations)) {
				throw new BusinessException('相同供应商的相同产品只能选择一次');
			}
			$combinations[] = $key;
		}
		
		// 提取明细数据
		$items = $data['items'];
		unset($data['items']);
		
		// 设置默认值
		$data['approval_status'] = DailyPriceOrder::STATUS_DRAFT;
		$data['total_items']     = count($items);
		
		Db::startTrans();
		try {
			// 创建报价单
			$orderId = $this->crudService->add($data);
			
			// 保存明细
			$this->saveOrderItems($orderId, $items);
			
			Db::commit();
			return $orderId;
		}
		catch (\Exception $e) {
			Db::rollback();
			throw new BusinessException('创建失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 重写编辑方法，处理报价明细
	 *
	 * @param array $data  数据
	 * @param array $where 条件
	 * @return bool
	 * @throws BusinessException
	 */
	public function edit(array $data, array $where): bool
	{
		// 获取报价单
		$order = $this->model->where($where)
		                     ->find();
		if (!$order) {
			throw new BusinessException('报价单不存在');
		}
		
		// 检查是否可以编辑
		if (!in_array($order->approval_status, [
			DailyPriceOrder::STATUS_DRAFT,
			DailyPriceOrder::STATUS_REJECTED,
			DailyPriceOrder::STATUS_RECALLED
		])) {
			throw new BusinessException('报价单当前状态不允许编辑');
		}
		
		// 验证明细数据
		if (empty($data['items']) || !is_array($data['items'])) {
			throw new BusinessException('报价明细不能为空');
		}
		
		// 验证明细数据中是否有重复的供应商-产品组合
		$combinations = [];
		foreach ($data['items'] as $item) {
			if (empty($item['supplier_id']) || empty($item['product_id'])) {
				continue; // 跳过空值
			}
			$key = $item['supplier_id'] . '-' . $item['product_id'];
			if (in_array($key, $combinations)) {
				throw new BusinessException('相同供应商的相同产品只能选择一次');
			}
			$combinations[] = $key;
		}
		
		// 提取明细数据
		$items = $data['items'];
		unset($data['items']);
		
		// 更新总数
		$data['total_items'] = count($items);
		
		Db::startTrans();
		try {
			// 更新报价单
			$result = $this->crudService->edit($data, $where);
			
			// 保存明细
			$this->saveOrderItems($order->id, $items);
			
			Db::commit();
			return $result;
		}
		catch (\Exception $e) {
			Db::rollback();
			throw new BusinessException('更新失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 保存报价单明细
	 *
	 * @param int   $orderId 报价单ID
	 * @param array $items   明细数据
	 * @throws \Exception
	 */
	private function saveOrderItems(int $orderId, array $items): void
	{
		// 删除原有明细
		$list = DailyPriceItem::where('order_id', $orderId)
		                      ->field('id')
		                      ->select();
		
		$list->delete();
		
		// 预处理明细数据
		// 使用模型层批量保存方法 - 自动处理租户隔离和创建人信息
		$insertCount = 0;
		foreach ($items as $index => $item) {
			$item['order_id']   = $orderId;
			$item['sort_order'] = $index + 1;
			
			// 确保价格变动字段的完整性
			if (isset($item['unit_price'])) {
				// 如果前端已经计算了价格变动数据，直接使用
				if (isset($item['old_price']) && isset($item['price_change']) && isset($item['change_rate'])) {
					// 前端已计算，直接使用
				}
				// 如果只有 old_price，重新计算
				elseif (isset($item['old_price'])) {
					$item['price_change'] = $item['unit_price'] - $item['old_price'];
					if ($item['old_price'] > 0) {
						$item['change_rate'] = ($item['price_change'] / $item['old_price']) * 100;
					}
					else {
						$item['change_rate'] = 0;
					}
				}
				// 如果没有 old_price，设置默认值
				else {
					$item['old_price']    = 0;
					$item['price_change'] = 0;
					$item['change_rate']  = 0;
				}
			}
			$data = [
				'order_id'        => $item['order_id'],
				'product_id'      => $item['product_id'],
				'supplier_id'     => $item['supplier_id'],
				'unit_price'      => $item['unit_price'],
				'old_price'       => $item['old_price'] ?? 0,
				'price_change'    => $item['price_change'] ?? 0,
				'change_rate'     => $item['change_rate'] ?? 0,
				'sort_order'      => $item['sort_order'] ?? 0,
				'stock_price'     => $item['stock_price'] ?? 0,
				'stock_qty'       => $item['stock_qty'] ?? 0,
				'policy_remark'   => $item['policy_remark'] ?? '',
				'is_manual_price' => $item['is_manual_price'] ?? 0,
			];
			
			$res = (new DailyPriceItem())->saveByCreate($data);
			if (!$res) {
				throw new \Exception('批量保存明细数据失败');
			}
			$insertCount++;
		}
		
		if ($insertCount !== count($items)) {
			throw new \Exception('批量保存明细数据失败');
		}
	}
	
	/**
	 * 获取详情（包含明细数据）
	 */
	public function getDetail(int $id)
	{
		return $this->model->with([
			'items' => [
				'product',
				'supplier'
			]
		])
		                   ->findOrEmpty($id);
	}
	
	/**
	 * 批量删除
	 *
	 * @param array|int $ids 要删除的ID数组或单个ID
	 * @return bool
	 */
	public function batchDelete($ids): bool
	{
		if (!is_array($ids)) {
			$ids = [$ids];
		}
		
		return $this->model->whereIn('id', $ids)
		                   ->delete();
	}
	
	/**
	 * 提交审批
	 */
	public function submitApproval(int $orderId): int|string
	{
		$order = DailyPriceOrder::find($orderId);
		if (!$order) {
			throw new BusinessException('报价单不存在');
		}
		
		if ($order->approval_status !== DailyPriceOrder::STATUS_DRAFT) {
			throw new BusinessException('只有草稿状态的报价单才能提交审批');
		}
		
		if ($order->total_items <= 0) {
			throw new BusinessException('请先添加报价明细');
		}
		
		try {
			// 使用统一工作流服务
			$unifiedWorkflowService = new UnifiedWorkflowService();
			return $unifiedWorkflowService->executeWorkflowOperation('submit', [
				'business_code' => 'daily_price_order',
				'business_id'   => $orderId,
				'operator_id'   => get_user_id()
			]);
		}
		catch (BusinessException $e) {
			Log::error('提交审批失败：' . $e->getMessage());
			throw new BusinessException($e->getMessage());
		}
	}
	
	/**
	 * 撤回审批（使用统一工作流服务）
	 */
	public function recallApproval(int $orderId): int|string
	{
		try {
			$unifiedWorkflowService = new UnifiedWorkflowService();
			return $unifiedWorkflowService->executeWorkflowOperation('withdraw', [
				'business_code' => 'daily_price_order',
				'business_id'   => $orderId,
				'operator_id'   => get_user_id(),
				'reason'        => '用户撤回申请'
			]);
		}
		catch (BusinessException $e) {
			Log::error('撤回审批失败：' . $e->getMessage());
			throw new BusinessException($e->getMessage());
		}
	}
	
	/**
	 * 作废审批（使用统一工作流服务）
	 */
	public function voidApproval(int $orderId, string $reason = ''): int|string
	{
		$unifiedWorkflowService = new UnifiedWorkflowService();
		return $unifiedWorkflowService->executeWorkflowOperation('void', [
			'business_code' => 'daily_price_order',
			'business_id'   => $orderId,
			'operator_id'   => get_user_id(),
			'reason'        => $reason
				?: '业务作废'
		]);
	}
	
	/**
	 * 获取供应商列表
	 */
	public function getSupplierList(): array
	{
		try {
			$suppliers = ImsSupplier::where('status', 1)
			                        ->field('id,name,contact_name,phone')
			                        ->order('id', 'desc')
			                        ->select();
			
			return [
				'list'  => $suppliers->toArray(),
				'total' => count($suppliers)
			];
		}
		catch (\Exception $e) {
			Log::error('获取供应商列表失败：' . $e->getMessage());
			return [
				'list'  => [],
				'total' => 0
			];
		}
	}
	
	/**
	 * 获取产品列表
	 */
	public function getProductList(int $supplierId = 0): array
	{
		try {
			$query = CrmProduct::where('status', 1);
			
			// 如果指定了供应商ID，按供应商筛选
			if ($supplierId > 0) {
				// 假设产品表有supplier_id字段，如果没有则忽略此条件
				if (CrmProduct::hasField('supplier_id')) {
					$query->where('supplier_id', $supplierId);
				}
			}
			
			$products = $query->field('id,name,spec,category_id,unit_id,price')
			                  ->order('id', 'desc')
			                  ->select();
			
			// 格式化产品数据，组合产品名称和规格
			$formattedProducts = [];
			foreach ($products as $product) {
				$productArray = $product->toArray();
				// 组合显示名称：产品名称-规格
				$productArray['display_name'] = $product->name . '-' . $product->spec;
				$formattedProducts[]          = $productArray;
			}
			
			return [
				'list'  => $formattedProducts,
				'total' => count($formattedProducts)
			];
		}
		catch (\Exception $e) {
			Log::error('获取产品列表失败：' . $e->getMessage());
			return [
				'list'  => [],
				'total' => 0
			];
		}
	}
	
	/**
	 * 获取昨日价格数据
	 */
	public function getYesterdayPrices(): array
	{
		try {
			// 计算昨日日期
			$yesterday = date('Y-m-d', strtotime('-3 day'));
			
			// 查询昨日已审批通过的报价单
			$yesterdayOrders = DailyPriceOrder::where('price_date', $yesterday)
			                                  ->where('approval_status', DailyPriceOrder::STATUS_APPROVED)
			                                  ->field('id')
			                                  ->select();
			
			if (empty($yesterdayOrders)) {
				return [];
			}
			
			$orderIds = array_column($yesterdayOrders->toArray(), 'id');
			
			// 查询昨日的价格明细
			$yesterdayItems = DailyPriceItem::whereIn('order_id', $orderIds)
			                                ->with([
				                                'supplier',
				                                'product'
			                                ])
			                                ->select();
			
			// 组织数据结构：supplier_id-product_id => price_data
			$priceData = [];
			foreach ($yesterdayItems as $item) {
				$key             = $item->supplier_id . '-' . $item->product_id;
				$priceData[$key] = [
					'price'         => floatval($item->unit_price),
					'stock_price'   => floatval($item->stock_price ?? 0),
					'stock_qty'     => floatval($item->stock_qty ?? 0),
					'policy_remark' => $item->policy_remark ?? '',
					'date'          => $yesterday,
					'supplier_id'   => $item->supplier_id,
					'product_id'    => $item->product_id,
					'supplier_name' => $item->supplier
						? $item->supplier->name
						: '',
					'product_name'  => $item->product
						? $item->product->name
						: '',
					'product_spec'  => $item->product
						? $item->product->spec
						: ''
				];
			}
			
			return $priceData;
		}
		catch (\Exception $e) {
			Log::error('获取昨日价格失败：' . $e->getMessage());
			return [];
		}
	}
	
	/**
	 * 获取统计数据
	 */
	public function getStatistics(array $params = []): array
	{
		$startDate = $params['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
		$endDate   = $params['end_date'] ?? date('Y-m-d');
		
		// 基础统计
		$totalOrders    = DailyPriceOrder::whereBetween('price_date', [
			$startDate,
			$endDate
		])
		                                 ->count();
		$approvedOrders = DailyPriceOrder::whereBetween('price_date', [
			$startDate,
			$endDate
		])
		                                 ->where('approval_status', DailyPriceOrder::STATUS_APPROVED)
		                                 ->count();
		$pendingOrders  = DailyPriceOrder::whereBetween('price_date', [
			$startDate,
			$endDate
		])
		                                 ->where('approval_status', DailyPriceOrder::STATUS_PENDING)
		                                 ->count();
		
		return [
			'total_orders'    => $totalOrders,
			'approved_orders' => $approvedOrders,
			'pending_orders'  => $pendingOrders,
			'approval_rate'   => $totalOrders > 0
				? round(($approvedOrders / $totalOrders) * 100, 2)
				: 0,
		];
	}
	
	
	/**
	 * 审批完成后的业务处理
	 */
	protected function afterApprovalComplete($order, int $status, string $opinion): void
	{
		/*if ($status === DailyPriceOrder::STATUS_APPROVED) {
		
		}*/
	}
	
	/**
	 * 检查指定日期的重复情况
	 *
	 * @param string   $date      日期
	 * @param string   $scene     场景：create=新增, submit=提交
	 * @param int|null $excludeId 排除的ID（编辑时使用）
	 * @return array
	 */
	public function checkDuplicateDate(string $date, string $scene = 'create', ?int $excludeId = null): array
	{
		$query = DailyPriceOrder::where('price_date', $date);
		
		// 排除当前记录（编辑时）
		if ($excludeId) {
			$query->where('id', '<>', $excludeId);
		}
		
		// 根据场景设置不同的状态检测
		switch ($scene) {
			case 'create':
				// 新增：检测所有状态（草稿、审批中、已通过）
				$query->whereIn('approval_status', [
					DailyPriceOrder::STATUS_DRAFT,
					// 0
					DailyPriceOrder::STATUS_PENDING,
					// 1
					DailyPriceOrder::STATUS_APPROVED
					// 2
				]);
				break;
			
			case 'submit':
				// 提交：只检测审批中和已通过状态
				$query->whereIn('approval_status', [
					DailyPriceOrder::STATUS_PENDING,
					// 1
					DailyPriceOrder::STATUS_APPROVED
					// 2
				]);
				break;
		}
		
		$existingOrder = $query->find();
		
		return [
			'exists'   => !empty($existingOrder),
			'order_id' => $existingOrder?->id,
			'status'   => $existingOrder?->approval_status,
			'message'  => $this->getDuplicateMessage($scene, $existingOrder)
		];
	}
	
	/**
	 * 获取重复检测的提示信息
	 */
	private function getDuplicateMessage(string $scene, $existingOrder): string
	{
		if (!$existingOrder) {
			return '';
		}
		
		$statusText = [
			              0 => '草稿',
			              1 => '审批中',
			              2 => '已通过'
		              ][$existingOrder->approval_status] ?? '未知';
		
		switch ($scene) {
			case 'create':
				return "当天已存在{$statusText}状态的报价单，不能重复创建";
			case 'submit':
				return "当天已存在{$statusText}状态的报价单，不能重复提交";
			default:
				return "当天已存在报价单";
		}
	}
	
	// ==================== FormServiceInterface 接口实现 ====================
	
	/**
	 * 获取表单数据
	 *
	 * @param int $id 表单ID
	 * @return array 表单详情数据
	 */
	public function getFormData(int $id): array
	{
		$order = $this->getDetail($id);
		if ($order->isEmpty()) {
			throw new BusinessException('每日报价单不存在');
		}
		return $order->toArray();
	}
	
	/**
	 * 保存表单数据
	 *
	 * @param array $data 表单数据
	 * @return array [id, formData]
	 */
	public function saveForm(array $data): array
	{
		// 1. 数据预处理和验证
		$data = $this->validateFormData($data, 'create');
		
		// 2. 设置默认值
		$data['approval_status']      = DailyPriceOrder::STATUS_DRAFT;
		$data['workflow_instance_id'] = 0;
		$data['submitter_id']         = $data['submitter_id'] ?? get_user_id();
		$data['price_date']           = $data['price_date'] ?? date('Y-m-d');
		
		// 3. 使用模型层统一保存方法 - 自动处理租户隔离和创建人信息
		$orderModel = new DailyPriceOrder();
		$orderId    = $orderModel->saveByCreate($data);
		
		if (!$orderId) {
			throw new BusinessException('保存每日报价单失败');
		}
		
		// 4. 获取完整数据
		$orderData = $this->getFormData($orderId);
		
		Log::info('每日报价单保存成功', [
			'order_id'     => $orderId,
			'submitter_id' => $data['submitter_id'],
			'price_date'   => $data['price_date']
		]);
		
		return [
			$orderId,
			$orderData
		];
	}
	
	/**
	 * 更新表单数据
	 *
	 * @param int   $id   表单ID
	 * @param array $data 表单数据
	 * @return bool 是否成功
	 */
	public function updateForm(int $id, array $data): bool
	{
		// 1. 先查询记录，确保记录存在
		$order = $this->model->find($id);
		if (!$order) {
			throw new BusinessException('每日报价单不存在');
		}
		
		// 2. 检查是否可以编辑
		if (!in_array($order->approval_status, [
			DailyPriceOrder::STATUS_DRAFT,
			DailyPriceOrder::STATUS_REJECTED,
			DailyPriceOrder::STATUS_RECALLED
		])) {
			throw new BusinessException('当前状态不允许编辑');
		}
		
		// 3. 数据验证
		$data = $this->validateFormData($data, 'update');
		
		// 4. 使用模型层统一更新方法 - 自动处理权限字段保护
		$result = $order->saveByUpdate($data);
		
		if ($result) {
			Log::info('每日报价单更新成功', [
				'order_id'       => $id,
				'updated_fields' => array_keys($data)
			]);
		}
		
		return $result;
	}
	
	/**
	 * 更新表单状态
	 *
	 * @param int   $id     表单ID
	 * @param int   $status 状态值
	 * @param array $extra  额外数据
	 * @return bool 是否成功
	 */
	public function updateFormStatus(int $id, int $status, array $extra = []): bool
	{
		// 1. 检查记录是否存在
		$order = $this->model->find($id);
		if (!$order) {
			throw new BusinessException('每日报价单不存在');
		}
		
		// 2. 准备更新数据
		$updateData = [
			'approval_status' => $status,
			'updated_id'      => get_user_id()
		];
		
		// 3. 根据状态设置相关字段
		switch ($status) {
			case DailyPriceOrder::STATUS_PENDING:
				$updateData['submit_time'] = date('Y-m-d H:i:s');
				break;
			case DailyPriceOrder::STATUS_APPROVED:
			case DailyPriceOrder::STATUS_REJECTED:
			case DailyPriceOrder::STATUS_TERMINATED:
				$updateData['approval_time'] = date('Y-m-d H:i:s');
				break;
			case DailyPriceOrder::STATUS_VOIDED:
				$updateData['void_time']    = date('Y-m-d H:i:s');
				$updateData['void_user_id'] = get_user_id();
				if (!empty($extra['void_reason'])) {
					$updateData['void_reason'] = $extra['void_reason'];
				}
				break;
		}
		
		// 4. 使用模型层统一更新方法 - 自动处理权限字段保护
		$result = $order->saveByUpdate($updateData);
		
		if ($result) {
			Log::info('每日报价单状态更新成功', [
				'order_id'   => $id,
				'new_status' => $status,
				'extra_data' => $extra
			]);
			
			// 5. 审批通过后的业务处理
			if ($status === DailyPriceOrder::STATUS_APPROVED) {
				$this->afterApprovalComplete($order, $status, $extra['opinion'] ?? '');
			}
		}
		
		return $result;
	}
	
	/**
	 * 删除表单
	 *
	 * @param int $id 记录ID
	 * @return bool 是否成功
	 */
	public function deleteForm(int $id): bool
	{
		// 1. 检查记录是否存在
		$order = $this->model->find($id);
		if (!$order) {
			throw new BusinessException('每日报价单不存在');
		}
		
		// 2. 检查是否可以删除（只有草稿状态可以删除）
		if ($order->approval_status !== DailyPriceOrder::STATUS_DRAFT) {
			throw new BusinessException('只有草稿状态的报价单可以删除');
		}
		
		// 3. 执行删除（使用事务保证数据一致性）
		Db::startTrans();
		try {
			// 删除报价明细
			DailyPriceItem::where('order_id', $id)
			              ->delete();
			
			// 删除报价单
			$result = $this->model->where('id', $id)
			                      ->delete();
			
			Db::commit();
			
			if ($result !== false) {
				Log::info('每日报价单删除成功', ['order_id' => $id]);
			}
			
			return $result !== false;
		}
		catch (\Exception $e) {
			Db::rollback();
			Log::error('删除每日报价单失败：' . $e->getMessage());
			throw new BusinessException('删除失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 获取流程实例标题
	 *
	 * @param $formData
	 * @return string
	 */
	public function getInstanceTitle($formData): string
	{
		$priceDate = $formData['price_date'] ?? date('Y-m-d');
		return "每日报价审批-{$priceDate}";
	}
	
	/**
	 * 验证表单数据
	 *
	 * @param array  $data  表单数据
	 * @param string $scene 验证场景
	 * @return array 验证后的数据
	 * @throws BusinessException 验证失败时抛出异常
	 */
	public function validateFormData(array $data, string $scene = 'create'): array
	{
		// 1. 基础验证规则
		$rules = $this->getValidationRules($scene);
		
		// 2. 验证必需字段
		if ($scene === 'create') {
			if (empty($data['price_date'])) {
				throw new BusinessException('报价日期不能为空');
			}
			
			// 验证日期格式
			if (!strtotime($data['price_date'])) {
				throw new BusinessException('报价日期格式不正确');
			}
			
			// 验证是否重复
			$duplicate = $this->checkDuplicateDate($data['price_date'], 'create');
			if ($duplicate['exists']) {
				throw new BusinessException($duplicate['message']);
			}
		}
		
		// 3. 验证明细数据（如果提供）
		if (!empty($data['items']) && is_array($data['items'])) {
			$this->validateItems($data['items']);
		}
		
		// 4. 数据清理和格式化
		if (isset($data['price_date'])) {
			$data['price_date'] = date('Y-m-d', strtotime($data['price_date']));
		}
		
		if (isset($data['total_items'])) {
			$data['total_items'] = (int)$data['total_items'];
		}
		
		Log::info('每日报价单数据验证通过', [
			'scene'      => $scene,
			'price_date' => $data['price_date'] ?? null
		]);
		
		return $data;
	}
	
	/**
	 * 验证明细数据
	 *
	 * @param array $items 明细数据
	 * @throws BusinessException
	 */
	private function validateItems(array $items): void
	{
		if (empty($items)) {
			throw new BusinessException('报价明细不能为空');
		}
		
		// 验证明细数据中是否有重复的供应商-产品组合
		$combinations = [];
		foreach ($items as $item) {
			if (empty($item['supplier_id']) || empty($item['product_id'])) {
				continue; // 跳过空值
			}
			$key = $item['supplier_id'] . '-' . $item['product_id'];
			if (in_array($key, $combinations)) {
				throw new BusinessException('相同供应商的相同产品只能选择一次');
			}
			$combinations[] = $key;
		}
	}
	
	// ==================== 工作流回调处理 ====================
	
	/**
	 * 工作流状态变更后的业务处理（覆盖默认实现）
	 *
	 * @param int   $businessId 报价单ID
	 * @param int   $status     新的工作流状态
	 * @param array $extra      额外数据
	 * @return bool 处理结果
	 */
	public function afterWorkflowStatusChange(int $businessId, int $status, array $extra = []): bool
	{
		Log::info('每日报价单工作流状态变更处理', [
			'order_id'    => $businessId,
			'status'      => $status,
			'status_text' => $this->getWorkflowStatusText($status),
			'extra'       => $extra
		]);
		
		try {
			switch ($status) {
				case WorkflowStatusConstant::STATUS_COMPLETED:
					// 审批通过后的处理
					return $this->handlePriceOrderApproved($businessId, $extra);
				
				case WorkflowStatusConstant::STATUS_REJECTED:
					// 审批拒绝后的处理
					return $this->handlePriceOrderRejected($businessId, $extra);
				
				case WorkflowStatusConstant::STATUS_RECALLED:
					// 撤回后的处理
					return $this->handlePriceOrderRecalled($businessId, $extra);
				
				case WorkflowStatusConstant::STATUS_VOID:
					// 作废后的处理
					return $this->handlePriceOrderVoided($businessId, $extra);
				
				default:
					// 其他状态使用默认处理
					return $this->afterWorkflowStatusChange($businessId, $status, $extra);
			}
			
		}
		catch (\Exception $e) {
			Log::error('每日报价单工作流状态变更处理失败', [
				'order_id' => $businessId,
				'status'   => $status,
				'error'    => $e->getMessage(),
				'trace'    => $e->getTraceAsString()
			]);
			
			return false;
		}
	}
	
	/**
	 * 处理报价单审批通过
	 *
	 * @param int   $orderId 报价单ID
	 * @param array $extra   额外数据
	 * @return bool 处理结果
	 */
	private function handlePriceOrderApproved(int $orderId, array $extra): bool
	{
		Log::info('每日报价单审批通过处理', [
			'order_id' => $orderId,
			'extra'    => $extra
		]);
		
		// 审批通过的具体业务逻辑：
		// 1. 发送通知给相关人员
		// 2. 更新价格历史记录
		// 3. 触发后续业务流程
		// 4. 同步到其他系统
		
		return true;
	}
	
	/**
	 * 处理报价单审批拒绝
	 *
	 * @param int   $orderId 报价单ID
	 * @param array $extra   额外数据
	 * @return bool 处理结果
	 */
	private function handlePriceOrderRejected(int $orderId, array $extra): bool
	{
		Log::info('每日报价单审批拒绝处理', [
			'order_id' => $orderId,
			'extra'    => $extra
		]);
		
		// 审批拒绝的具体业务逻辑：
		// 1. 发送拒绝通知
		// 2. 记录拒绝原因
		// 3. 提醒修改后重新提交
		
		return true;
	}
	
	/**
	 * 处理报价单撤回
	 *
	 * @param int   $orderId 报价单ID
	 * @param array $extra   额外数据
	 * @return bool 处理结果
	 */
	private function handlePriceOrderRecalled(int $orderId, array $extra): bool
	{
		Log::info('每日报价单撤回处理', [
			'order_id' => $orderId,
			'extra'    => $extra
		]);
		
		// 撤回的具体业务逻辑：
		// 1. 清理相关的临时数据
		// 2. 发送撤回通知
		// 3. 恢复到可编辑状态
		
		return true;
	}
	
	/**
	 * 处理报价单作废
	 *
	 * @param int   $orderId 报价单ID
	 * @param array $extra   额外数据
	 * @return bool 处理结果
	 */
	private function handlePriceOrderVoided(int $orderId, array $extra): bool
	{
		Log::info('每日报价单作废处理', [
			'order_id' => $orderId,
			'extra'    => $extra
		]);
		
		// 作废的具体业务逻辑：
		// 1. 标记为作废状态
		// 2. 发送作废通知
		// 3. 清理相关数据
		
		return true;
	}
	
}