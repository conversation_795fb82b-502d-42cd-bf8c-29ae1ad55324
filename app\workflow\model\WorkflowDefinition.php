<?php
declare(strict_types=1);

namespace app\workflow\model;

use app\common\core\base\BaseModel;

/**
 * 工作流程定义表模型
 */
class WorkflowDefinition extends BaseModel
{
	// 设置表名
	protected $name = 'workflow_definition';
	
	// 设置主键
	protected $pk = 'id';
	
	/**
	 * 将配置值转换为 JSON 字符串
	 *
	 * @param mixed $value 需要转换的值（支持数组、对象等）
	 * @return string 如果值为空 返回空字符串，否则返回 JSON 编码后的字符串
	 * @throws \InvalidArgumentException 如果 json_encode 失败
	 */
	public function setFlowConfigAttr($value): string
	{
		if (empty($value)) {
			return '';
		}
		
		$json = json_encode($value, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK);
		if ($json === false) {
			return '';
		}
		
		return $json;
	}
	
	
	public function getFlowConfigAttr($value): array
	{
		if (empty($value)) {
			return [];
		}

		// 先解码HTML实体
		$decodedValue = htmlspecialchars_decode($value);

		// 尝试解析 JSON
		$decoded = json_decode($decodedValue, true);

		// 检查 JSON 解析是否成功
		if (json_last_error() !== JSON_ERROR_NONE) {
			// JSON 解析失败，记录错误并返回空数组
			\think\facade\Log::warning('WorkflowDefinition flow_config JSON 解析失败', [
				'value' => $value,
				'decoded_value' => $decodedValue,
				'error' => json_last_error_msg(),
				'definition_id' => $this->getKey()
			]);
			return [];
		}

		// 确保返回的是数组类型
		if (!is_array($decoded)) {
			\think\facade\Log::warning('WorkflowDefinition flow_config 不是数组类型', [
				'value' => $value,
				'decoded_type' => gettype($decoded),
				'definition_id' => $this->getKey()
			]);
			return [];
		}

		return $decoded;
	}
	
	public function types()
	{
		return $this->belongsTo(WorkflowType::class, 'type_id', 'id')
		            ->bind([
			            'type_name' => 'name',
			            'module_code',
			            'business_code'
		            ]);
	}

	/**
	 * 获取默认搜索字段
	 *
	 * @return array
	 */
	public function getDefaultSearchFields(): array
	{
		return [
			'name' => ['type' => 'like'],
			'type_id' => ['type' => 'eq'],
			'status' => ['type' => 'eq'],
			'created_at' => ['type' => 'date'],
		];
	}
} 