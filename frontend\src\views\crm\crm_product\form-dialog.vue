<script setup lang="ts">
  import { ElMessage, FormInstance } from 'element-plus'
  import { CrmProductApi } from '@/api/crm/crmProduct'
  import { ApiStatus } from '@/utils/http/status'
  import ApiSelect from '@/components/core/forms/ApiSelect/index.vue'
  import FormMediaSelector from '@/components/custom/FormMediaSelector/index.vue'
  import SupplierSelector from '@/components/business/SupplierSelector.vue'

  const emit = defineEmits(['success'])

  // 对话框状态
  const dialogVisible = ref(false)
  const dialogType = ref('add') // add或edit
  const loading = ref(false)

  // 表单引用
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive({
    category_id: null,
    unit_id: null,
    supplier_id: null,
    spec: '',
    name: '',
    code: '',
    images: '',
    // price: 0,
    // cost: 0,
    description: '',
    status: 1
  })

  // 表单验证规则
  const rules = {
    category_id: [
      {
        required: true,
        message: '请选择产品分类',
        trigger: 'change'
      }
    ],
    unit_id: [
      {
        required: true,
        message: '请选择计量单位',
        trigger: 'change'
      }
    ],
    supplier_id: [
      {
        required: true,
        message: '请选择供应商',
        trigger: 'change'
      }
    ],
    name: [
      {
        required: true,
        message: '名称不能为空',
        trigger: 'blur'
      },
      {
        max: 200,
        message: '名称长度不能超过200个字符',
        trigger: 'blur'
      }
    ],
    code: [
      {
        max: 50,
        message: '编码长度不能超过50个字符',
        trigger: 'blur'
      }
    ]
    /*price: [
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value === '' || value === null || value === undefined) {
            callback(new Error('售价不能为空'))
          } else if (isNaN(Number(value))) {
            callback(new Error('售价必须是数字'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    cost: [
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value === '' || value === null || value === undefined) {
            callback(new Error('成本价不能为空'))
          } else if (isNaN(Number(value))) {
            callback(new Error('成本价必须是数字'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]*/
  }

  // 显示对话框
  const showDialog = async (type: string, id?: number) => {
    dialogType.value = type
    dialogVisible.value = true

    // 重置表单数据
    Object.assign(formData, {
      category_id: null,
      unit_id: null,
      spec: '',
      name: '',
      code: '',
      images: '',
      // price: 0,
      // cost: 0,
      description: '',
      status: 1
    })

    // 编辑模式下获取详情数据
    if (type === 'edit' && id) {
      try {
        loading.value = true
        const res = await CrmProductApi.detail(id)
        if (res.code === ApiStatus.success) {
          // 处理数据类型转换
          const data = { ...res.data }

          /*{
            // 将字符串格式的 price 转换为数字
            if (data.price && typeof data.price === 'string') {
              data.price = parseFloat(data.price)
            }
          }

          {
            // 将字符串格式的 cost 转换为数字
            if (data.cost && typeof data.cost === 'string') {
              data.cost = parseFloat(data.cost)
            }
          }*/

          Object.assign(formData, data)
        }
      } finally {
        loading.value = false
      }
    }
  }

  // 供应商变更处理
  const handleSupplierChange = (supplierId: any) => {
    console.log('选择的供应商ID:', supplierId)
    // 可以在这里根据供应商重置一些字段或执行其他逻辑
  }

  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          loading.value = true

          // 处理提交数据转换
          const submitData = { ...formData }

          let res

          if (dialogType.value === 'add') {
            res = await CrmProductApi.add(submitData)
          } else {
            res = await CrmProductApi.update(submitData)
          }

          if (res.code === ApiStatus.success) {
            ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
            dialogVisible.value = false
            emit('success')
          }
        } finally {
          loading.value = false
        }
      }
    })
  }

  // 暴露方法给父组件
  defineExpose({
    showDialog
  })
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogType === 'add' ? '新增产品表' : '编辑产品表'"
    width="800px"
    top="5vh"
    destroy-on-close
  >
    <div class="dialog-content">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        v-loading="loading"
      >
        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="产品分类" prop="category_id">
              <ApiSelect
                v-model="formData.category_id"
                :api="{ url: '/crm/crm_product_category/options' }"
                placeholder="请选择产品分类"
                clearable
                :auto-load="true"
                :load-on-focus="false"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="计量单位" prop="unit_id">
              <ApiSelect
                v-model="formData.unit_id"
                :api="{ url: '/crm/crm_product_unit/options' }"
                placeholder="请选择计量单位"
                clearable
                :auto-load="true"
                :load-on-focus="false"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="供应商" prop="supplier_id">
              <SupplierSelector
                v-model="formData.supplier_id"
                @change="handleSupplierChange"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="产品规格" prop="spec">
              <ElInput v-model="formData.spec" placeholder="请输入产品规格" />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="产品名称" prop="name">
              <ElInput v-model="formData.name" placeholder="请输入产品名称" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="产品编码" prop="code">
              <ElInput v-model="formData.code" placeholder="请输入产品编码" />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="24">
            <ElFormItem label="产品图片" prop="images">
              <FormMediaSelector
                v-model="formData.images"
                type="image"
                :multiple="true"
                :max-count="5"
                placeholder="请选择产品图片"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <!--        <ElRow :gutter="20">
                  <ElCol :span="12">
                    <ElFormItem label="售价" prop="price">
                      <ElInputNumber
                        v-model="formData.price"
                        placeholder="请输入售价"
                        style="width: 100%"
                        controls-position="right"
                        :min="0"
                        :precision="2"
                      />
                    </ElFormItem>
                  </ElCol>
                  <ElCol :span="12">
                    <ElFormItem label="成本价" prop="cost">
                      <ElInputNumber
                        v-model="formData.cost"
                        placeholder="请输入成本价"
                        style="width: 100%"
                        controls-position="right"
                        :min="0"
                        :precision="2"
                      />
                    </ElFormItem>
                  </ElCol>
                </ElRow>-->

        <ElRow :gutter="20">
          <ElCol :span="24">
            <ElFormItem label="产品描述" prop="description">
              <ElInput
                v-model="formData.description"
                type="textarea"
                :rows="4"
                placeholder="请输入产品描述"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="状态" prop="status">
              <ElRadioGroup v-model="formData.status">
                <ElRadio :value="1">启用</ElRadio>
                <ElRadio :value="0">停用</ElRadio>
              </ElRadioGroup>
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="dialogVisible = false">取消</ElButton>
        <ElButton type="primary" @click="submitForm" :loading="loading">确定</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
  .dialog-content {
    max-height: calc(90vh - 200px);
    overflow-y: auto;
    padding: 0 20px;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    padding: 10px 20px;
    border-top: 1px solid #e4e7ed;
  }

  .avatar-uploader .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }

  .avatar-uploader .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    text-align: center;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    line-height: 100px;
  }
</style>
