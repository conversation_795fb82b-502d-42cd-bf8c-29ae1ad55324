<?php
declare(strict_types=1);

namespace app\workflow\model;

use app\common\core\base\BaseModel;
use app\system\model\AdminModel;
use app\system\model\DeptModel;
use think\model\relation\BelongsTo;

/**
 * 工作流程实例表模型
 */
class WorkflowInstance extends BaseModel
{
	// 设置表名
	protected $name = 'workflow_instance';
	
	protected string $dataRangeField = 'submitter_id';
	
	// 使用类型转换替代 json 配置，以保持数字类型
	/*protected $type = [
		'form_data' => 'json',
		'cc_users' => 'json',
		'process_data' => 'json'
	];*/
	
	/**
	 * JSON 字段设置器 - 保持数字类型
	 *
	 * @param mixed $value 要设置的值
	 * @return string JSON 字符串
	 */
	public function setFormDataAttr($value): string
	{
		if (empty($value)) {
			return '';
		}
		
		// 使用 JSON_NUMERIC_CHECK 保持数字类型
		return json_encode($value, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK);
	}
	
	/**
	 * JSON 字段获取器 - 保持数字类型
	 *
	 * @param string $value JSON 字符串
	 * @return array 解析后的数组，保持数字类型
	 */
	public function getFormDataAttr($value): array
	{
		if (empty($value)) {
			return [];
		}

		// 尝试解析 JSON
		$decoded = json_decode($value, true);

		// 检查 JSON 解析是否成功
		if (json_last_error() !== JSON_ERROR_NONE) {
			// JSON 解析失败，记录错误并返回空数组
			\think\facade\Log::warning('WorkflowInstance form_data JSON 解析失败', [
				'value' => substr($value, 0, 200),
				'error' => json_last_error_msg(),
				'instance_id' => $this->getKey()
			]);
			return [];
		}

		// 处理双重编码问题：如果第一次解析得到字符串，尝试再次解析
		if (is_string($decoded)) {
			\think\facade\Log::info('WorkflowInstance form_data 检测到双重编码，尝试二次解析', [
				'instance_id' => $this->getKey(),
				'first_decode_preview' => substr($decoded, 0, 100)
			]);

			$secondDecoded = json_decode($decoded, true);

			if (json_last_error() === JSON_ERROR_NONE && is_array($secondDecoded)) {
				// 二次解析成功，返回数组
				return $secondDecoded;
			} else {
				// 二次解析失败，包装成数组返回
				\think\facade\Log::warning('WorkflowInstance form_data 二次解析失败', [
					'instance_id' => $this->getKey(),
					'second_decode_error' => json_last_error_msg()
				]);
				return ['_raw_data' => $decoded];
			}
		}

		// 确保返回的是数组类型
		if (!is_array($decoded)) {
			\think\facade\Log::warning('WorkflowInstance form_data 不是数组类型', [
				'decoded_type' => gettype($decoded),
				'instance_id' => $this->getKey()
			]);
			return ['_raw_data' => $decoded];
		}

		return $decoded;
	}
	
	/**
	 * JSON 字段设置器 - 保持数字类型
	 *
	 * @param mixed $value 要设置的值
	 * @return string JSON 字符串
	 */
	public function setCcUsersAttr($value): string
	{
		if (empty($value)) {
			return '';
		}
		
		// 使用 JSON_NUMERIC_CHECK 保持数字类型
		return json_encode($value, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK);
	}
	
	/**
	 * JSON 字段获取器 - 保持数字类型
	 *
	 * @param string $value JSON 字符串
	 * @return array 解析后的数组，保持数字类型
	 */
	public function getCcUsersAttr($value): array
	{
		if (empty($value)) {
			return [];
		}

		// 尝试解析 JSON
		$decoded = json_decode($value, true);

		// 检查 JSON 解析是否成功
		if (json_last_error() !== JSON_ERROR_NONE) {
			// JSON 解析失败，记录错误并返回空数组
			\think\facade\Log::warning('WorkflowInstance cc_users JSON 解析失败', [
				'value' => $value,
				'error' => json_last_error_msg(),
				'instance_id' => $this->getKey()
			]);
			return [];
		}

		// 确保返回的是数组类型
		if (!is_array($decoded)) {
			\think\facade\Log::warning('WorkflowInstance cc_users 不是数组类型', [
				'value' => $value,
				'decoded_type' => gettype($decoded),
				'instance_id' => $this->getKey()
			]);
			return [];
		}

		return $decoded;
	}
	
	/**
	 * JSON 字段设置器 - 保持数字类型
	 *
	 * @param mixed $value 要设置的值
	 * @return string JSON 字符串
	 */
	public function setProcessDataAttr($value): string
	{
		if (empty($value)) {
			return '';
		}
		
		// 使用 JSON_NUMERIC_CHECK 保持数字类型
		return json_encode($value, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK);
	}
	
	/**
	 * JSON 字段获取器 - 保持数字类型
	 *
	 * @param string $value JSON 字符串
	 * @return array 解析后的数组，保持数字类型
	 */
	public function getProcessDataAttr($value): array
	{
		if (empty($value)) {
			return [];
		}

		// 尝试解析 JSON
		$decoded = json_decode($value, true);

		// 检查 JSON 解析是否成功
		if (json_last_error() !== JSON_ERROR_NONE) {
			// JSON 解析失败，记录错误并返回空数组
			\think\facade\Log::warning('WorkflowInstance process_data JSON 解析失败', [
				'value' => $value,
				'error' => json_last_error_msg(),
				'instance_id' => $this->getKey()
			]);
			return [];
		}

		// 确保返回的是数组类型
		if (!is_array($decoded)) {
			\think\facade\Log::warning('WorkflowInstance process_data 不是数组类型', [
				'value' => $value,
				'decoded_type' => gettype($decoded),
				'instance_id' => $this->getKey()
			]);
			return [];
		}

		return $decoded;
	}
	
	public function getDefaultSearchFields(): array
	{
		return [
			'title'         => ['type' => 'like'],
			'status'        => ['type' => 'eq'],
			'created_at'    => ['type' => 'date'],
			'submitter_id'  => ['type' => 'eq'],
			'business_code' => ['type' => 'in'],
		];
	}
	
	/**
	 * 关联流程定义
	 *
	 * @return BelongsTo
	 */
	public function definition(): BelongsTo
	{
		return $this->belongsTo(WorkflowDefinition::class, 'definition_id', 'id')
		            ->bind([
			            'definition_name' => 'name',
		            ]);
	}
	
	
	/**
	 * 关联流程类型
	 *
	 * @return BelongsTo
	 */
	public function types(): BelongsTo
	{
		return $this->belongsTo(WorkflowType::class, 'type_id', 'id')
		            ->bind([
			            'type_name' => 'name',
			            'module_code'
		            ]);
	}
	
	/**
	 * 关联提交人
	 *
	 * @return BelongsTo
	 */
	public function submitter(): BelongsTo
	{
		return $this->belongsTo(AdminModel::class, 'submitter_id', 'id')
		            ->bind([
			            'submitter_name' => 'real_name'
		            ]);
	}
	
	/**
	 * 关联提交人部门
	 *
	 * @return BelongsTo
	 */
	public function dept(): BelongsTo
	{
		return $this->belongsTo(DeptModel::class, 'submitter_dept_id', 'id')
		            ->bind([
			            'dept_name' => 'name'
		            ]);
	}
	
	/**
	 * 关联历史记录
	 */
	public function flowLogs()
	{
		return $this->hasMany('WorkflowHistory', 'instance_id', 'id')
		            ->order('operation_time', 'desc');
	}
} 