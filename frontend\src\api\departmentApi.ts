import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

// 部门管理
export class DepartmentApi {
  // 获取部门列表
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: `/system/department/index`,
      params
    })
  }

  // 获取部门树形结构
  static options() {
    return request.get<BaseResult>({
      url: `/system/department/options`
    })
  }

  // 新增部门
  static add(params: any) {
    return request.post<BaseResult>({
      url: `/system/department/add`,
      data: params
    })
  }

  // 编辑部门
  static edit(id: number, params: any) {
    return request.post<BaseResult>({
      url: `/system/department/edit/${id}`,
      data: params
    })
  }

  // 获取部门详情
  static detail(id: number) {
    return request.get<BaseResult>({
      url: `/system/department/detail/${id}`
    })
  }

  // 删除部门
  static delete(id: number) {
    return request.post<BaseResult>({
      url: `/system/department/delete/${id}`
    })
  }
}
