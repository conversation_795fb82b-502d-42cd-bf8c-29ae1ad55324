<template>
  <ArtTableFullScreen>
    <div class="department-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="formFilters"
        :items="formItems"
        @reset="handleReset"
        @search="handleSearch"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader :columnList="columnOptions" @refresh="handleRefresh">
          <template #left>
            <ElButton
              v-if="hasAuth('system:permission_department:add')"
              @click="showDialog('add')"
              icon="Plus"
              type="primary"
              v-ripple
              >新增
            </ElButton>
            <ElButton @click="toggleExpand" v-ripple>
              {{ isExpanded ? '收起' : '展开' }}
            </ElButton>
          </template>
        </ArtTableHeader>

        <!-- 表格 -->
        <ArtTable
          ref="tableRef"
          :loading="loading"
          :data="tableData"
          :pagination="false"
          :marginTop="10"
          row-key="id"
          :default-expand-all="isExpanded"
        >
          <template #default>
            <!-- 部门名称列 -->
            <ElTableColumn prop="name" label="部门名称" min-width="200" />

            <!-- 联系人列 -->
            <ElTableColumn prop="leader_name" label="联系人" min-width="200">
              <template #default="{ row }">
                <div v-if="row.leader_name || row.phone || row.email">
                  <p v-if="row.leader_name">姓名：{{ row.leader_name }}</p>
                  <p v-if="row.phone">手机号：{{ row.phone }}</p>
                  <p v-if="row.email">E-mail：{{ row.email }}</p>
                </div>
              </template>
            </ElTableColumn>

            <!-- 排序列 -->
            <ElTableColumn prop="sort" label="排序" width="80" />

            <!-- 状态列 -->
            <ElTableColumn prop="status" label="状态" width="100">
              <template #default="{ row }">
                <ElTag :type="getStatusTagType(row.status)">
                  {{ buildStatusTagText(row.status) }}
                </ElTag>
              </template>
            </ElTableColumn>

            <!-- 创建时间列 -->
            <ElTableColumn prop="created_at" label="创建时间" sortable />

            <!-- 备注列 -->
            <ElTableColumn prop="remark" label="备注" />

            <!-- 操作列 -->
            <ElTableColumn prop="operation" label="操作" width="200">
              <template #default="{ row }">
                <div>
                  <ArtButtonTable
                    v-if="hasAuth('system:permission_department:edit')"
                    text="编辑"
                    type="edit"
                    @click="showDialog('edit', row)"
                  />
                  <ArtButtonTable
                    v-if="hasAuth('system:permission_department:delete')"
                    text="删除"
                    type="delete"
                    @click="deleteDepartment(row.id)"
                  />
                </div>
              </template>
            </ElTableColumn>
          </template>
        </ArtTable>

        <ElDialog
          v-model="dialogVisible"
          :title="dialogType === 'add' ? '添加部门' : '编辑部门'"
          width="30%"
        >
          <ElForm ref="formRef" :model="formData" :rules="rules" label-width="100">
            <ElFormItem label="上级部门" prop="parent_id">
              <ElTreeSelect
                :key="`tree-select-${dialogType}-${departmentTree.length}`"
                v-model="formData.parent_id"
                :data="departmentTree"
                :props="{ label: 'name', value: 'id', children: 'children' }"
                check-strictly
                default-expand-all
                placeholder="请选择上级部门"
                :render-after-expand="false"
                clearable
                filterable
                :filter-node-method="filterNode"
              />
            </ElFormItem>
            <ElFormItem label="部门名称" prop="name">
              <ElInput v-model="formData.name" clearable />
            </ElFormItem>
            <ElFormItem label="联系人姓名" prop="leader_name">
              <ElInput v-model="formData.leader_name" clearable />
            </ElFormItem>
            <ElFormItem label="手机号" prop="phone">
              <ElInput v-model="formData.phone" clearable />
            </ElFormItem>
            <ElFormItem label="E-mail" prop="email">
              <ElInput v-model="formData.email" clearable />
            </ElFormItem>
            <ElFormItem label="排序" prop="sort">
              <ElInputNumber v-model="formData.sort" />
            </ElFormItem>
            <ElFormItem label="状态" prop="status">
              <ElSwitch
                v-model="formData.status"
                :active-value="1"
                :inactive-value="0"
                active-text="启用"
                inactive-text="禁用"
              />
            </ElFormItem>
            <ElFormItem label="备注" prop="remark">
              <ElInput v-model="formData.remark" type="textarea" clearable />
            </ElFormItem>
          </ElForm>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="dialogVisible = false">取消</ElButton>
              <ElButton type="primary" @click="handleSubmit">提交</ElButton>
            </div>
          </template>
        </ElDialog>
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<script setup lang="ts">
  import { SearchChangeParams, SearchFormItem } from '@/types/search-form'
  import { ElDialog, FormInstance, ElTag, ElTreeSelect, ElButton } from 'element-plus'
  import { ElMessageBox, ElMessage } from 'element-plus'
  import type { FormRules } from 'element-plus'

  import ArtButtonTable from '@/components/core/forms/ArtButtonTable.vue'
  import { DepartmentApi } from '@/api/departmentApi'
  import { useAuth } from '@/composables/useAuth'

  // 权限验证
  const { hasAuth } = useAuth()

  const dialogType = ref('add')
  const dialogVisible = ref(false)
  const loading = ref(false)
  const departmentTree = ref<any[]>([])
  const departmentId = ref(0)

  // 定义表单搜索初始值
  const initialSearchState = {
    name: '',
    status: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    console.log('搜索参数:', formFilters)
    getTableData()
  }

  // 表单项变更处理
  const handleFormChange = (params: SearchChangeParams): void => {
    console.log('表单项变更:', params)
  }

  // 表单配置项
  const formItems: SearchFormItem[] = [
    {
      label: '部门名称',
      prop: 'name',
      type: 'input',
      config: {
        clearable: true
      },
      onChange: handleFormChange
    },
    {
      label: '状态',
      prop: 'status',
      type: 'select',
      config: {
        clearable: true
      },
      options: () => [
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' }
      ],
      onChange: handleFormChange
    }
  ]

  // 列配置
  const columnOptions = [
    { label: '部门名称', prop: 'name' },
    { label: '联系人', prop: 'leader_name' },
    { label: '排序', prop: 'sort' },
    { label: '状态', prop: 'status' },
    { label: '创建时间', prop: 'created_at' },
    { label: '备注', prop: 'remark' },
    { label: '操作', prop: 'operation' }
  ]

  // 获取标签类型
  // 1: 启用 0: 禁用
  const getStatusTagType = (status: number) => {
    return status === 1 ? 'success' : 'danger'
  }

  // 构建标签文本
  const buildStatusTagText = (status: number) => {
    return status === 1 ? '启用' : '禁用'
  }

  // 表单实例
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive({
    name: '',
    sort: 0,
    parent_id: null as number | null,
    phone: '',
    email: '',
    leader_name: '',
    status: 1,
    remark: ''
  })

  // 表格数据
  const tableData = ref<any[]>([])

  onMounted(async () => {
    await getTableData()
    // toggleExpand()
    await getDepartmentTree()
  })

  // 树节点过滤方法
  const filterNode = (value: string, data: any) => {
    if (!value) return true
    return data.name.includes(value)
  }

  // 获取部门树
  const getDepartmentTree = async () => {
    try {
      const res = await DepartmentApi.options()
      if (res.code === 1) {
        // 确保数据结构正确
        const treeData = res.data || []
        departmentTree.value = treeData
        // 确保数据更新后再进行下一步操作
        await nextTick()
      }
    } catch (error) {
      console.error('获取部门树失败:', error)
      departmentTree.value = []
    }
  }

  const getTableData = async () => {
    loading.value = true
    const res = await DepartmentApi.list({
      ...formFilters
    })
    loading.value = false
    if (res.code === 1) {
      tableData.value = res.data || []
    }
  }

  const handleRefresh = () => {
    getTableData()
  }

  // 显示对话框
  const showDialog = async (type: string, row?: any) => {
    dialogVisible.value = true
    dialogType.value = type

    // 使用nextTick确保DOM更新完成
    await nextTick()

    if (type === 'edit' && row) {
      console.log('row', row)
      formData.name = row.name || ''
      formData.sort = row.sort || 0
      formData.parent_id = row.parent_id === 0 ? null : (row.parent_id || null)
      formData.status = row.status ?? 1
      formData.remark = row.remark || ''
      formData.leader_name = row.leader_name || ''
      formData.phone = row.phone || ''
      formData.email = row.email || ''
      departmentId.value = row.id
    } else {
      // 重置表单数据
      Object.assign(formData, {
        name: '',
        sort: 0,
        parent_id: null,
        status: 1,
        remark: '',
        leader_name: '',
        phone: '',
        email: ''
      })
      departmentId.value = 0
    }
  }

  // 删除部门
  const deleteDepartment = (id: number) => {
    ElMessageBox.confirm('确定要删除该部门吗？', '删除部门', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    }).then(async () => {
      const res = await DepartmentApi.delete(id)
      if (res.code === 1) {
        ElMessage.success('删除成功')
        await getDepartmentTree()
        await getTableData()
      }
    })
  }

  // 表单验证规则
  const rules = reactive<FormRules>({
    name: [
      { required: true, message: '请输入部门名称', trigger: 'blur' },
      { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
    ]
  })

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        let res
        if (dialogType.value === 'add') {
          res = await DepartmentApi.add(formData)
        } else {
          res = await DepartmentApi.edit(departmentId.value, formData)
        }
        dialogVisible.value = false
        if (res.code === 1) {
          ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
          await getDepartmentTree()
          await getTableData()
        }
      }
    })
  }

  const isExpanded = ref(false)
  const tableRef = ref()

  const toggleExpand = () => {
    isExpanded.value = !isExpanded.value
    nextTick(() => {
      if (tableRef.value) {
        tableRef.value[isExpanded.value ? 'expandAll' : 'collapseAll']()
      }
    })
  }
</script>

<style lang="scss" scoped>
  .department-page {
    width: 100%;
  }
</style>
